import React from 'react'
import Card from '../ui/Card'

interface TodoItem {
  id: string
  text: string
  completed: boolean
}

interface TodoListProps {
  title?: string
  items: TodoItem[]
  onToggle?: (id: string) => void
  className?: string
}

export default function TodoList({ 
  title = "今日待办", 
  items, 
  onToggle,
  className = '' 
}: TodoListProps) {
  return (
    <Card variant="todo" className={`my-3 ${className}`}>
      <div className="text-base font-semibold text-yellow-800 mb-3 flex items-center gap-2">
        <i className="fas fa-tasks"></i>
        {title}
      </div>
      
      {items.map((item) => (
        <div key={item.id} className="flex items-center mb-2 last:mb-0">
          <button
            onClick={() => onToggle?.(item.id)}
            className={`w-4 h-4 border-2 border-yellow-500 rounded-sm mr-2 flex items-center justify-center flex-shrink-0 transition-colors duration-200 ${
              item.completed ? 'bg-yellow-500 text-white' : 'bg-transparent'
            }`}
          >
            {item.completed && <i className="fas fa-check text-xs"></i>}
          </button>
          <span className={`text-sm text-yellow-800 ${item.completed ? 'line-through opacity-60' : ''}`}>
            {item.text}
          </span>
        </div>
      ))}
    </Card>
  )
}
