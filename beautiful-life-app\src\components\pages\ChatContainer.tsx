import React, { useState, useRef, useEffect } from 'react'
import ChatMessage from './ChatMessage'
import VisionCard from './VisionCard'
import StepsList from './StepsList'
import TodoList from './TodoList'
import TypingIndicator from './TypingIndicator'
import Input from '../ui/Input'
import Button from '../ui/Button'

interface Message {
  id: string
  type: 'user' | 'ai'
  content: React.ReactNode
  timestamp?: string
}

interface ChatContainerProps {
  className?: string
}

export default function ChatContainer({ className = '' }: ChatContainerProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  
  // Mock initial messages
  useEffect(() => {
    const initialMessages: Message[] = [
      {
        id: '1',
        type: 'ai',
        content: '你好！我是你的生活愿景助手。我可以帮助你规划和实现你的人生目标。请告诉我你的愿景或梦想吧！',
        timestamp: '今天 9:30'
      },
      {
        id: '2',
        type: 'user',
        content: '我想在30岁前在东南亚海边买房，实现远程工作的自由生活'
      },
      {
        id: '3',
        type: 'ai',
        content: (
          <>
            <VisionCard 
              title="🏖️ 东南亚海边生活愿景"
              content="在30岁前在泰国或马来西亚购买海景房，实现远程工作的自由生活"
            />
            <div className="mt-3">
              这是一个很棒的愿景！让我为你制定一个实现计划：
            </div>
            <StepsList 
              steps={[
                { number: 1, content: '提升远程工作技能，确保稳定收入来源' },
                { number: 2, content: '研究目标国家的房产政策和投资环境' },
                { number: 3, content: '制定储蓄计划，准备首付资金' },
                { number: 4, content: '学习当地语言和文化' },
                { number: 5, content: '实地考察，选择合适的房产' }
              ]}
            />
            <TodoList 
              title="本周行动清单"
              items={[
                { id: '1', text: '研究泰国和马来西亚的房产政策', completed: false },
                { id: '2', text: '制定月度储蓄目标', completed: true },
                { id: '3', text: '开始学习泰语基础', completed: false },
                { id: '4', text: '寻找远程工作机会', completed: false }
              ]}
            />
          </>
        )
      }
    ]
    setMessages(initialMessages)
  }, [])
  
  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }
  
  useEffect(() => {
    scrollToBottom()
  }, [messages, isTyping])
  
  const handleSend = () => {
    if (!inputValue.trim()) return
    
    const newMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue
    }
    
    setMessages(prev => [...prev, newMessage])
    setInputValue('')
    setIsTyping(true)
    
    // Simulate AI response
    setTimeout(() => {
      setIsTyping(false)
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: '感谢你的分享！我正在为你分析这个愿景，并制定详细的实现计划...'
      }
      setMessages(prev => [...prev, aiResponse])
    }, 2000)
  }
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }
  
  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Chat Messages */}
      <div 
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto px-5 pt-4 pb-20 scroll-smooth"
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: '#c7c7cc transparent'
        }}
      >
        {messages.map((message) => (
          <ChatMessage
            key={message.id}
            type={message.type}
            content={message.content}
            timestamp={message.timestamp}
          />
        ))}
        {isTyping && <TypingIndicator />}
      </div>
      
      {/* Input Area */}
      <div className="absolute bottom-0 left-0 right-0 bg-white border-t border-ios-gray-100 px-5 py-3 pb-8 z-50">
        <div className="flex items-end gap-3 max-w-[393px] mx-auto">
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="描述你的生活愿景..."
            className="flex-1"
          />
          <Button
            variant="primary"
            size="md"
            onClick={handleSend}
            disabled={!inputValue.trim()}
            className="w-9 h-9 rounded-[18px] flex-shrink-0"
          >
            <i className="fas fa-arrow-up"></i>
          </Button>
        </div>
      </div>
    </div>
  )
}
