import React from 'react'

interface StatusBarProps {
  textColor?: 'white' | 'black'
}

export default function StatusBar({ textColor = 'white' }: StatusBarProps) {
  const textColorClass = textColor === 'white' ? 'text-white' : 'text-black'
  
  return (
    <div className={`h-[54px] flex justify-between items-center px-6 absolute top-0 left-0 right-0 z-50 ${textColorClass} text-[17px] font-semibold`}>
      {/* Left side - Time */}
      <div className="flex-1 flex items-center">
        <span className="text-[17px] font-semibold tracking-tight">9:41</span>
      </div>
      
      {/* Center - Dynamic Island */}
      <div className="flex-none flex justify-center items-center">
        <div className="w-[126px] h-[37px] bg-black/85 rounded-[19px] backdrop-blur-xl border border-white/10"></div>
      </div>
      
      {/* Right side - Status icons */}
      <div className="flex-1 flex justify-end items-center">
        <div className="flex items-center gap-1.5">
          {/* Cellular Signal */}
          <div className="flex items-center gap-0.5">
            <div className="w-1 h-1 bg-current rounded-full"></div>
            <div className="w-1 h-1.5 bg-current rounded-full"></div>
            <div className="w-1 h-2 bg-current rounded-full"></div>
            <div className="w-1 h-2.5 bg-current rounded-full"></div>
          </div>
          
          {/* WiFi Signal */}
          <div className="w-4 h-3 relative">
            <svg width="15" height="11" viewBox="0 0 15 11" fill="none" className="w-full h-full">
              <path d="M7.5 0C11.64 0 15 1.79 15 4v3c0 2.21-3.36 4-7.5 4S0 9.21 0 7V4c0-2.21 3.36-4 7.5-4z" fill="currentColor" opacity="0.3"/>
              <path d="M7.5 2C10.26 2 12.5 3.12 12.5 4.5v2c0 1.38-2.24 2.5-5 2.5s-5-1.12-5-2.5v-2C2.5 3.12 4.74 2 7.5 2z" fill="currentColor" opacity="0.6"/>
              <path d="M7.5 4C9.16 4 10.5 4.67 10.5 5.5v1c0 0.83-1.34 1.5-3 1.5s-3-0.67-3-1.5v-1C4.5 4.67 5.84 4 7.5 4z" fill="currentColor"/>
            </svg>
          </div>
          
          {/* Battery */}
          <div className="flex items-center">
            <div className="w-6 h-3 border border-current rounded-sm relative">
              <div className="w-4 h-1.5 bg-current rounded-sm absolute top-0.5 left-0.5"></div>
            </div>
            <div className="w-0.5 h-1 bg-current rounded-r-sm ml-0.5"></div>
          </div>
        </div>
      </div>
    </div>
  )
}
