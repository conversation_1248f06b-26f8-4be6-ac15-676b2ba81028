import React from 'react'
import Avatar from '../ui/Avatar'

export default function TypingIndicator() {
  return (
    <div className="mb-6 fade-in-up">
      <div className="flex justify-start">
        <div className="mr-2 mt-1 flex-shrink-0">
          <Avatar size="sm" variant="ai" />
        </div>
        
        <div className="flex items-center px-4 py-3 bg-white border border-ios-gray-100 rounded-[18px] rounded-bl-md max-w-[280px] shadow-sm">
          <div className="flex gap-1">
            <div className="w-2 h-2 rounded-full bg-ios-gray-300 typing-pulse"></div>
            <div className="w-2 h-2 rounded-full bg-ios-gray-300 typing-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-2 h-2 rounded-full bg-ios-gray-300 typing-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
        </div>
      </div>
    </div>
  )
}
