import React from 'react'

interface NavigationHeaderProps {
  title: string
  onMenuClick?: () => void
  leftButton?: React.ReactNode
  rightButtons?: React.ReactNode[]
}

export default function NavigationHeader({ 
  title, 
  onMenuClick, 
  leftButton,
  rightButtons = []
}: NavigationHeaderProps) {
  return (
    <div className="bg-white h-[88px] pt-[54px] pb-4 px-5 border-b border-ios-gray-100 flex items-center justify-between relative z-40">
      {/* Left side */}
      <div className="flex items-center gap-3">
        {leftButton || (
          <button 
            onClick={onMenuClick}
            className="w-8 h-8 rounded-2xl bg-ios-gray-50 border-0 flex items-center justify-center text-ios-blue text-base cursor-pointer transition-all duration-200 hover:bg-ios-gray-100 hover:scale-105"
          >
            <i className="fas fa-bars"></i>
          </button>
        )}
        <h1 className="text-xl font-semibold text-ios-gray-900 tracking-tight">{title}</h1>
      </div>
      
      {/* Right side */}
      {rightButtons.length > 0 && (
        <div className="flex gap-4">
          {rightButtons.map((button, index) => (
            <div key={index}>{button}</div>
          ))}
        </div>
      )}
    </div>
  )
}
