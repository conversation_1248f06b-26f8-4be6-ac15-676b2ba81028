# 美好生活 App - 高保真移动应用原型

## 项目概述

这是一个高保真的移动应用原型项目，模拟 iPhone 15 Pro 的设计规范，展示"美好生活"应用的各个界面。项目使用纯 HTML + CSS + JavaScript 实现，无需任何框架依赖。

## 项目结构

```
├── index.html              # 主入口页面，展示所有原型界面
├── assets/                 # 静态资源目录
│   ├── css/
│   │   └── mobile-app.css  # 全局移动端样式
│   ├── images/             # 图片资源（预留）
│   └── js/                 # JavaScript文件（预留）
├── pages/                  # 各个页面原型
│   └── splash.html         # 启动加载界面
└── README.md              # 项目说明文档
```

## 已完成的界面

### 1. 启动加载界面 (splash.html)
- **设计特点**：
  - 使用真实的东南亚海滩背景图片（来自 Unsplash）
  - 动态渐变色彩过渡动画
  - 浮动图标动效（太阳、沙滩伞、鸡尾酒、棕榈树）
  - iOS 状态栏完美模拟
  - 品牌 Logo 和加载指示器
  - 响应式设计，适配不同屏幕尺寸

- **技术实现**：
  - CSS3 动画和过渡效果
  - 背景图片叠加渐变遮罩
  - Flexbox 布局
  - Font Awesome 图标库
  - 模拟 iPhone 15 Pro 尺寸 (393 x 852 px)

## 设计规范

### iPhone 15 Pro 规格
- **屏幕尺寸**: 393 x 852 像素
- **圆角半径**: 47px (外框) / 39px (屏幕)
- **状态栏高度**: 54px
- **字体**: SF Pro Display (使用系统字体栈)

### 色彩方案
- **主色调**: 渐变色彩（粉色到蓝色过渡）
- **背景**: 真实海滩图片 + 渐变遮罩
- **文字**: 白色，带阴影效果
- **按钮**: 毛玻璃效果，半透明背景

### 动画效果
- **渐变动画**: 8秒循环的背景色彩变化
- **浮动动画**: 6秒循环的图标浮动效果
- **加载动画**: 点状加载指示器
- **淡入动画**: 页面元素逐步显示

## 使用方法

1. **直接打开**: 在浏览器中打开 `index.html` 文件
2. **本地服务器**: 推荐使用本地服务器运行以获得最佳体验
   ```bash
   # 使用 Python
   python -m http.server 8000
   
   # 使用 Node.js
   npx serve .
   
   # 使用 PHP
   php -S localhost:8000
   ```
3. **访问**: 打开浏览器访问 `http://localhost:8000`

## 浏览器兼容性

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ⚠️ IE 不支持（使用了现代 CSS 特性）

## 响应式设计

项目针对以下设备进行了优化：
- **桌面端**: 完整的原型展示网格
- **平板端**: 自适应网格布局
- **移动端**: 单列布局，保持原型比例

## 开发计划

### 即将开发的界面：
1. **主页界面** - 应用核心功能展示
2. **个人资料页** - 用户信息管理
3. **设置界面** - 应用配置选项
4. **功能页面** - 根据具体需求定制

### 技术优化：
- [ ] 添加页面间的转场动画
- [ ] 实现更多交互效果
- [ ] 优化加载性能
- [ ] 添加暗色主题支持

## 自定义指南

### 修改颜色主题
在 `assets/css/mobile-app.css` 中修改 CSS 变量：
```css
.gradient-sunset {
    background: linear-gradient(135deg, #your-color1, #your-color2);
}
```

### 添加新页面
1. 在 `pages/` 目录下创建新的 HTML 文件
2. 在 `index.html` 中添加对应的展示卡片
3. 使用相同的 iPhone 容器结构

### 更换背景图片
在 `splash.html` 中修改背景图片 URL：
```css
.beach-scene {
    background: url('your-image-url') center/cover;
}
```

## 技术栈

- **HTML5**: 语义化标记
- **CSS3**: 现代样式特性（Grid、Flexbox、动画）
- **JavaScript**: 原生 ES6+
- **Font Awesome**: 图标库
- **Unsplash**: 高质量图片资源

## 许可证

本项目仅用于原型展示和学习目的。图片资源来自 Unsplash，遵循其使用条款。

---

**开发者**: AI Assistant  
**最后更新**: 2025-07-02  
**版本**: v1.0.0
