import React from 'react'

interface IPhoneContainerProps {
  children: React.ReactNode
}

export default function IPhoneContainer({ children }: IPhoneContainerProps) {
  return (
    <div className="w-[393px] h-[852px] bg-black rounded-[47px] p-2 shadow-2xl relative">
      <div className="w-full h-full bg-white rounded-[39px] overflow-hidden relative">
        {children}
      </div>
    </div>
  )
}
