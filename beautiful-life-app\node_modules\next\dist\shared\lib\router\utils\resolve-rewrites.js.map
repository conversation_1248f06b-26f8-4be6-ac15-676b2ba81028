{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/resolve-rewrites.ts"], "names": ["resolveRewrites", "<PERSON><PERSON><PERSON>", "pages", "rewrites", "query", "resolveHref", "locales", "matchedPage", "externalDest", "parsedAs", "parseRelativeUrl", "fsPathname", "removeTrailingSlash", "normalizeLocalePath", "removeBasePath", "pathname", "resolvedHref", "handleRewrite", "rewrite", "matcher", "getPathMatch", "source", "process", "env", "__NEXT_TRAILING_SLASH", "removeUnnamedP<PERSON>ms", "strict", "params", "has", "missing", "hasParams", "matchHas", "headers", "host", "document", "location", "hostname", "navigator", "userAgent", "cookies", "cookie", "split", "reduce", "acc", "item", "key", "value", "join", "Object", "assign", "destination", "destRes", "prepareDestination", "appendParamsToQuery", "parsedDestination", "newUrl", "includes", "finished", "i", "beforeFiles", "length", "afterFiles", "fallback"], "mappings": ";;;;+BASA;;;eAAwBA;;;2BAPK;oCACgB;qCACT;qCACA;gCACL;kCACE;AAElB,SAASA,gBACtBC,MAAc,EACdC,KAAe,EACfC,QAIC,EACDC,KAAqB,EACrBC,WAAqC,EACrCC,OAAkB;IAQlB,IAAIC,cAAc;IAClB,IAAIC,eAAe;IACnB,IAAIC,WAAWC,IAAAA,kCAAgB,EAACT;IAChC,IAAIU,aAAaC,IAAAA,wCAAmB,EAClCC,IAAAA,wCAAmB,EAACC,IAAAA,8BAAc,EAACL,SAASM,QAAQ,GAAGT,SAASS,QAAQ;IAE1E,IAAIC;IAEJ,MAAMC,gBAAgB,CAACC;QACrB,MAAMC,UAAUC,IAAAA,uBAAY,EAC1BF,QAAQG,MAAM,GAAIC,CAAAA,QAAQC,GAAG,CAACC,qBAAqB,GAAG,SAAS,EAAC,GAChE;YACEC,qBAAqB;YACrBC,QAAQ;QACV;QAGF,IAAIC,SAASR,QAAQV,SAASM,QAAQ;QAEtC,IAAI,AAACG,CAAAA,QAAQU,GAAG,IAAIV,QAAQW,OAAO,AAAD,KAAMF,QAAQ;YAC9C,MAAMG,YAAYC,IAAAA,4BAAQ,EACxB;gBACEC,SAAS;oBACPC,MAAMC,SAASC,QAAQ,CAACC,QAAQ;oBAChC,cAAcC,UAAUC,SAAS;gBACnC;gBACAC,SAASL,SAASM,MAAM,CACrBC,KAAK,CAAC,MACNC,MAAM,CAAyB,CAACC,KAAKC;oBACpC,MAAM,CAACC,KAAK,GAAGC,MAAM,GAAGF,KAAKH,KAAK,CAAC;oBACnCE,GAAG,CAACE,IAAI,GAAGC,MAAMC,IAAI,CAAC;oBACtB,OAAOJ;gBACT,GAAG,CAAC;YACR,GACAlC,SAASL,KAAK,EACdc,QAAQU,GAAG,EACXV,QAAQW,OAAO;YAGjB,IAAIC,WAAW;gBACbkB,OAAOC,MAAM,CAACtB,QAAQG;YACxB,OAAO;gBACLH,SAAS;YACX;QACF;QAEA,IAAIA,QAAQ;YACV,IAAI,CAACT,QAAQgC,WAAW,EAAE;gBACxB,8DAA8D;gBAC9D1C,eAAe;gBACf,OAAO;YACT;YACA,MAAM2C,UAAUC,IAAAA,sCAAkB,EAAC;gBACjCC,qBAAqB;gBACrBH,aAAahC,QAAQgC,WAAW;gBAChCvB,QAAQA;gBACRvB,OAAOA;YACT;YACAK,WAAW0C,QAAQG,iBAAiB;YACpCrD,SAASkD,QAAQI,MAAM;YACvBP,OAAOC,MAAM,CAAC7C,OAAO+C,QAAQG,iBAAiB,CAAClD,KAAK;YAEpDO,aAAaC,IAAAA,wCAAmB,EAC9BC,IAAAA,wCAAmB,EAACC,IAAAA,8BAAc,EAACb,SAASK,SAASS,QAAQ;YAG/D,IAAIb,MAAMsD,QAAQ,CAAC7C,aAAa;gBAC9B,yDAAyD;gBACzD,yBAAyB;gBACzBJ,cAAc;gBACdS,eAAeL;gBACf,OAAO;YACT;YAEA,uEAAuE;YACvEK,eAAeX,YAAYM;YAE3B,IAAIK,iBAAiBf,UAAUC,MAAMsD,QAAQ,CAACxC,eAAe;gBAC3DT,cAAc;gBACd,OAAO;YACT;QACF;IACF;IACA,IAAIkD,WAAW;IAEf,IAAK,IAAIC,IAAI,GAAGA,IAAIvD,SAASwD,WAAW,CAACC,MAAM,EAAEF,IAAK;QACpD,mDAAmD;QACnD,8CAA8C;QAC9CzC,cAAcd,SAASwD,WAAW,CAACD,EAAE;IACvC;IACAnD,cAAcL,MAAMsD,QAAQ,CAAC7C;IAE7B,IAAI,CAACJ,aAAa;QAChB,IAAI,CAACkD,UAAU;YACb,IAAK,IAAIC,IAAI,GAAGA,IAAIvD,SAAS0D,UAAU,CAACD,MAAM,EAAEF,IAAK;gBACnD,IAAIzC,cAAcd,SAAS0D,UAAU,CAACH,EAAE,GAAG;oBACzCD,WAAW;oBACX;gBACF;YACF;QACF;QAEA,0DAA0D;QAC1D,IAAI,CAACA,UAAU;YACbzC,eAAeX,YAAYM;YAC3BJ,cAAcL,MAAMsD,QAAQ,CAACxC;YAC7ByC,WAAWlD;QACb;QAEA,IAAI,CAACkD,UAAU;YACb,IAAK,IAAIC,IAAI,GAAGA,IAAIvD,SAAS2D,QAAQ,CAACF,MAAM,EAAEF,IAAK;gBACjD,IAAIzC,cAAcd,SAAS2D,QAAQ,CAACJ,EAAE,GAAG;oBACvCD,WAAW;oBACX;gBACF;YACF;QACF;IACF;IAEA,OAAO;QACLxD;QACAQ;QACAF;QACAS;QACAR;IACF;AACF"}