import React, { forwardRef } from 'react'

interface InputProps {
  placeholder?: string
  value?: string
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
  disabled?: boolean
  className?: string
  rows?: number
  maxRows?: number
}

const Input = forwardRef<HTMLTextAreaElement, InputProps>(({
  placeholder,
  value,
  onChange,
  onKeyDown,
  disabled = false,
  className = '',
  rows = 1,
  maxRows = 5
}, ref) => {
  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    // Auto-resize functionality
    const target = e.target
    target.style.height = 'auto'
    const scrollHeight = target.scrollHeight
    const lineHeight = 24 // Approximate line height
    const maxHeight = lineHeight * maxRows
    target.style.height = Math.min(scrollHeight, maxHeight) + 'px'
    
    if (onChange) {
      onChange(e)
    }
  }
  
  return (
    <textarea
      ref={ref}
      value={value}
      onChange={handleInput}
      onKeyDown={onKeyDown}
      placeholder={placeholder}
      disabled={disabled}
      rows={rows}
      className={`
        flex-1 min-h-[36px] max-h-[120px] px-4 py-2 
        border border-ios-gray-100 rounded-[18px] 
        text-base leading-relaxed resize-none outline-none 
        bg-gray-50 transition-all duration-200
        focus:border-ios-blue focus:bg-white focus:shadow-sm focus:ring-2 focus:ring-ios-blue/10
        disabled:bg-ios-gray-100 disabled:cursor-not-allowed
        ${className}
      `.trim()}
    />
  )
})

Input.displayName = 'Input'

export default Input
