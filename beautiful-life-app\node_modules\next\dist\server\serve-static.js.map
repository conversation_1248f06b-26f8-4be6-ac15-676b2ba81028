{"version": 3, "sources": ["../../src/server/serve-static.ts"], "names": ["serveStatic", "getContentType", "getExtension", "send", "mime", "define", "req", "res", "path", "opts", "Promise", "resolve", "reject", "on", "err", "Error", "code", "pipe", "extWithoutDot", "getType", "lookup", "contentType", "extension"], "mappings": ";;;;;;;;;;;;;;;;IASgBA,WAAW;eAAXA;;IAoBAC,cAAc;eAAdA;;IAUAC,YAAY;eAAZA;;;6DAtCC;;;;;;AAEjB,gGAAgG;AAChG,0FAA0F;AAC1FC,aAAI,CAACC,IAAI,CAACC,MAAM,CAAC;IACf,cAAc;QAAC;KAAO;AACxB;AAEO,SAASL,YACdM,GAAoB,EACpBC,GAAmB,EACnBC,IAAY,EACZC,IAAiC;IAEjC,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3BT,IAAAA,aAAI,EAACG,KAAKE,MAAMC,MACbI,EAAE,CAAC,aAAa;YACf,yCAAyC;YACzC,MAAMC,MAAW,IAAIC,MAAM;YAC3BD,IAAIE,IAAI,GAAG;YACXJ,OAAOE;QACT,GACCD,EAAE,CAAC,SAASD,QACZK,IAAI,CAACV,KACLM,EAAE,CAAC,UAAUF;IAClB;AACF;AAEO,SAASV,eAAeiB,aAAqB;IAClD,MAAM,EAAEd,IAAI,EAAE,GAAGD,aAAI;IACrB,IAAI,aAAaC,MAAM;QACrB,MAAM;QACN,OAAOA,KAAKe,OAAO,CAACD;IACtB;IACA,MAAM;IACN,OAAO,AAACd,KAAagB,MAAM,CAACF;AAC9B;AAEO,SAAShB,aAAamB,WAAmB;IAC9C,MAAM,EAAEjB,IAAI,EAAE,GAAGD,aAAI;IACrB,IAAI,kBAAkBC,MAAM;QAC1B,MAAM;QACN,OAAOA,KAAKF,YAAY,CAACmB;IAC3B;IACA,MAAM;IACN,OAAO,AAACjB,KAAakB,SAAS,CAACD;AACjC"}