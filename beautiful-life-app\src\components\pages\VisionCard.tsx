import React from 'react'
import Card from '../ui/Card'

interface VisionCardProps {
  title: string
  content: string
  className?: string
}

export default function VisionCard({ title, content, className = '' }: VisionCardProps) {
  return (
    <Card variant="vision" className={`my-4 ${className}`}>
      <div className="text-lg font-semibold mb-2">{title}</div>
      <div className="text-[15px] leading-relaxed opacity-95">{content}</div>
    </Card>
  )
}
