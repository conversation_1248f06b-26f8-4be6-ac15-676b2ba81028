{"version": 3, "sources": ["../../../../src/server/web/spec-extension/revalidate-tag.ts"], "names": ["revalidateTag", "tag", "staticGenerationAsyncStorage", "fetch", "__nextGetStaticStore", "store", "getStore", "incrementalCache", "Error", "staticGenerationBailout", "revalidatedTags", "includes", "push", "pendingRevalidates", "catch", "err", "console", "error", "pathWasRevalidated"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;yCAFwB;AAEjC,SAASA,cAAcC,GAAW;IACvC,MAAMC,+BAA+B,AACnCC,MACAC,oBAAoB,oBAFe,AACnCD,MACAC,oBAAoB,MADpBD;IAGF,MAAME,QACJH,gDAAAA,6BAA8BI,QAAQ;IAExC,IAAI,CAACD,SAAS,CAACA,MAAME,gBAAgB,EAAE;QACrC,MAAM,IAAIC,MACR,CAAC,4DAA4D,EAAEP,IAAI,CAAC;IAExE;IAEA,2EAA2E;IAC3E,oDAAoD;IACpDQ,IAAAA,gDAAuB,EAAC,CAAC,cAAc,EAAER,IAAI,CAAC;IAE9C,IAAI,CAACI,MAAMK,eAAe,EAAE;QAC1BL,MAAMK,eAAe,GAAG,EAAE;IAC5B;IACA,IAAI,CAACL,MAAMK,eAAe,CAACC,QAAQ,CAACV,MAAM;QACxCI,MAAMK,eAAe,CAACE,IAAI,CAACX;IAC7B;IAEA,IAAI,CAACI,MAAMQ,kBAAkB,EAAE;QAC7BR,MAAMQ,kBAAkB,GAAG,CAAC;IAC9B;IACAR,MAAMQ,kBAAkB,CAACZ,IAAI,GAAGI,MAAME,gBAAgB,CACnDP,aAAa,oBADgBK,MAAME,gBAAgB,CACnDP,aAAa,MADgBK,MAAME,gBAAgB,EACnCN,KAChBa,KAAK,CAAC,CAACC;QACNC,QAAQC,KAAK,CAAC,CAAC,yBAAyB,EAAEhB,IAAI,CAAC,EAAEc;IACnD;IAEF,4CAA4C;IAC5CV,MAAMa,kBAAkB,GAAG;AAC7B"}