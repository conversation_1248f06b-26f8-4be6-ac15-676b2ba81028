<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>启动加载 - 美好生活</title>
    <link rel="stylesheet" href="../assets/css/mobile-app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .splash-container {
            height: 100%;
            background: linear-gradient(
                135deg,
                #ff9a9e 0%,
                #fad0c4 25%,
                #a8edea 50%,
                #fed6e3 75%,
                #ff9a9e 100%
            );
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding-top: 54px; /* 为状态栏留出空间 */
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .beach-scene {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80') center/cover;
            opacity: 0.7;
            z-index: 1;
        }

        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                45deg,
                rgba(255, 154, 158, 0.8) 0%,
                rgba(250, 208, 196, 0.6) 50%,
                rgba(168, 237, 234, 0.8) 100%
            );
            z-index: 2;
        }

        .content {
            position: relative;
            z-index: 3;
            text-align: center;
            padding: 0 40px;
            margin-top: -27px; /* 调整内容位置，考虑状态栏高度 */
        }

        .logo-container {
            margin-bottom: 60px;
            animation: fadeInUp 1.5s ease-out;
        }

        .app-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 26.67px; /* iOS App图标圆角比例 */
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            backdrop-filter: blur(30px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.12),
                0 2px 8px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .app-logo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
            border-radius: 26.67px 26.67px 0 0;
        }

        .app-logo i {
            font-size: 60px;
            color: #fff;
            text-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .brand-text {
            color: #fff;
            margin-bottom: 80px;
        }

        .brand-title {
            font-size: 34px;
            font-weight: 700; /* iOS使用的是700而不是800 */
            margin-bottom: 8px;
            text-shadow: 0 2px 12px rgba(0, 0, 0, 0.25);
            letter-spacing: -0.4px; /* iOS典型的字母间距 */
            line-height: 1.1;
        }

        .brand-subtitle {
            font-size: 17px; /* iOS标准字体大小 */
            font-weight: 400;
            opacity: 0.9;
            text-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
            line-height: 1.35; /* iOS标准行高 */
            letter-spacing: -0.2px;
        }

        .loading-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            animation: fadeInUp 2s ease-out 0.5s both;
        }

        .loading-text {
            color: rgba(255, 255, 255, 0.85);
            font-size: 15px; /* iOS标准辅助文字大小 */
            font-weight: 500;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
            letter-spacing: -0.1px;
        }

        .loading-dots {
            display: flex;
            gap: 6px;
            align-items: center;
        }

        .dot {
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            animation: dotPulse 1.2s infinite ease-in-out;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .dot:nth-child(1) { animation-delay: 0s; }
        .dot:nth-child(2) { animation-delay: 0.2s; }
        .dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes dotPulse {
            0%, 60%, 100% {
                transform: scale(1);
                opacity: 0.8;
            }
            30% {
                transform: scale(1.3);
                opacity: 1;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2;
            pointer-events: none;
        }

        .floating-icon {
            position: absolute;
            color: rgba(255, 255, 255, 0.3);
            animation: float 6s ease-in-out infinite;
        }

        .floating-icon:nth-child(1) {
            top: 15%;
            left: 10%;
            font-size: 24px;
            animation-delay: 0s;
        }

        .floating-icon:nth-child(2) {
            top: 25%;
            right: 15%;
            font-size: 20px;
            animation-delay: 1s;
        }

        .floating-icon:nth-child(3) {
            bottom: 30%;
            left: 20%;
            font-size: 28px;
            animation-delay: 2s;
        }

        .floating-icon:nth-child(4) {
            bottom: 20%;
            right: 10%;
            font-size: 22px;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 0.6;
            }
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- iOS Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="time">9:41</span>
                </div>
                <div class="status-center">
                    <!-- Dynamic Island / Notch Area -->
                    <div class="dynamic-island"></div>
                </div>
                <div class="status-right">
                    <div class="status-icons">
                        <div class="cellular-signal">
                            <div class="signal-dot"></div>
                            <div class="signal-dot"></div>
                            <div class="signal-dot"></div>
                            <div class="signal-dot"></div>
                        </div>
                        <div class="wifi-signal">
                            <svg width="15" height="11" viewBox="0 0 15 11" fill="none">
                                <path d="M7.5 0C11.64 0 15 1.79 15 4v3c0 2.21-3.36 4-7.5 4S0 9.21 0 7V4c0-2.21 3.36-4 7.5-4z" fill="currentColor" opacity="0.3"/>
                                <path d="M7.5 2C10.26 2 12.5 3.12 12.5 4.5v2c0 1.38-2.24 2.5-5 2.5s-5-1.12-5-2.5v-2C2.5 3.12 4.74 2 7.5 2z" fill="currentColor" opacity="0.6"/>
                                <path d="M7.5 4C9.16 4 10.5 4.67 10.5 5.5v1c0 0.83-1.34 1.5-3 1.5s-3-0.67-3-1.5v-1C4.5 4.67 5.84 4 7.5 4z" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="battery-indicator">
                            <div class="battery-body">
                                <div class="battery-level"></div>
                            </div>
                            <div class="battery-tip"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- App Content -->
            <div class="app-content">
                <div class="splash-container">
                    <!-- Background Beach Scene -->
                    <div class="beach-scene"></div>
                    <div class="overlay"></div>

                    <!-- Floating Elements -->
                    <div class="floating-elements">
                        <i class="fas fa-sun floating-icon"></i>
                        <i class="fas fa-umbrella-beach floating-icon"></i>
                        <i class="fas fa-cocktail floating-icon"></i>
                        <i class="fas fa-palm-tree floating-icon"></i>
                    </div>

                    <!-- Main Content -->
                    <div class="content">
                        <div class="logo-container">
                            <div class="app-logo">
                                <i class="fas fa-sun"></i>
                            </div>
                            <div class="brand-text">
                                <h1 class="brand-title">美好生活</h1>
                                <p class="brand-subtitle">发现生活中的每一份美好<br>享受阳光与海滩的惬意时光</p>
                            </div>
                        </div>

                        <div class="loading-section">
                            <p class="loading-text">正在为您准备美好体验...</p>
                            <div class="loading-dots">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟加载过程
        document.addEventListener('DOMContentLoaded', function() {
            // 3秒后可以跳转到主界面（这里只是演示）
            setTimeout(function() {
                console.log('加载完成，可以跳转到主界面');
                // 实际应用中这里会跳转到主界面
                // window.location.href = 'home.html';
            }, 3000);
        });
    </script>
</body>
</html>
