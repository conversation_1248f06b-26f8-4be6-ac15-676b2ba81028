import React from 'react'

interface MenuItem {
  icon: string
  label: string
  onClick?: () => void
}

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
  userName?: string
  userStatus?: string
  currentVision?: {
    title: string
    description: string
  }
  menuItems?: MenuItem[]
}

export default function Sidebar({ 
  isOpen, 
  onClose, 
  userName = "张小明",
  userStatus = "25岁 · 程序员",
  currentVision = {
    title: "🏖️ 东南亚海边生活",
    description: "在30岁前在泰国或马来西亚购买海景房，实现远程工作的自由生活"
  },
  menuItems = [
    { icon: "fas fa-heart", label: "我的愿景" },
    { icon: "fas fa-brain", label: "洞察" },
    { icon: "fas fa-cog", label: "设置" }
  ]
}: SidebarProps) {
  return (
    <>
      {/* Overlay */}
      <div 
        className={`fixed top-0 left-0 w-full h-full bg-black/50 transition-all duration-300 z-[999] ${
          isOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
        }`}
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className={`fixed top-0 left-0 w-[280px] h-full bg-white border-r border-ios-gray-100 transform transition-transform duration-300 z-[1000] overflow-hidden ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="w-[280px] h-full p-5 overflow-y-auto">
          {/* User Info */}
          <div className="text-center mb-6 pb-5 border-b border-ios-gray-50">
            <div className="w-20 h-20 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center mx-auto mb-3 text-white text-2xl">
              <i className="fas fa-user"></i>
            </div>
            <div className="text-lg font-semibold text-ios-gray-900 mb-1">{userName}</div>
            <div className="text-sm text-ios-gray-500">{userStatus}</div>
          </div>
          
          {/* Current Vision */}
          <div className="mb-6">
            <div className="text-base font-semibold text-ios-gray-900 mb-3 flex items-center gap-2">
              <i className="fas fa-star"></i>
              当前愿景
            </div>
            <div className="bg-gradient-to-br from-pink-400 to-purple-300 rounded-xl p-4 text-white">
              <h4 className="text-sm font-semibold mb-2 opacity-90">{currentVision.title}</h4>
              <p className="text-[13px] leading-relaxed opacity-85">{currentVision.description}</p>
            </div>
          </div>
          
          {/* Menu */}
          <div className="mb-6">
            <div className="text-base font-semibold text-ios-gray-900 mb-3 flex items-center gap-2">
              <i className="fas fa-th-large"></i>
              功能
            </div>
            <ul className="space-y-1">
              {menuItems.map((item, index) => (
                <li key={index}>
                  <button 
                    onClick={item.onClick}
                    className="w-full flex items-center p-3 rounded-lg text-ios-gray-900 transition-colors duration-200 hover:bg-ios-gray-50 cursor-pointer"
                  >
                    <i className={`${item.icon} w-5 mr-3 text-ios-gray-500`}></i>
                    {item.label}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </>
  )
}
