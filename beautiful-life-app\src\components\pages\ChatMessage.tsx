import React from 'react'
import Avatar from '../ui/Avatar'

interface ChatMessageProps {
  type: 'user' | 'ai'
  content: React.ReactNode
  timestamp?: string
  showAvatar?: boolean
}

export default function ChatMessage({ 
  type, 
  content, 
  timestamp,
  showAvatar = true 
}: ChatMessageProps) {
  const isUser = type === 'user'
  
  return (
    <div className="mb-6 fade-in-up">
      {timestamp && (
        <div className="text-xs text-ios-gray-500 text-center mb-4">
          {timestamp}
        </div>
      )}
      
      <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
        {!isUser && showAvatar && (
          <div className="mr-2 mt-1 flex-shrink-0">
            <Avatar size="sm" variant="ai" />
          </div>
        )}
        
        <div className={`max-w-[280px] px-4 py-3 rounded-[18px] text-base leading-relaxed relative ${
          isUser 
            ? 'bg-ios-blue text-white rounded-br-md' 
            : 'bg-white text-ios-gray-900 border border-ios-gray-100 rounded-bl-md shadow-sm'
        }`}>
          {content}
        </div>
      </div>
    </div>
  )
}
