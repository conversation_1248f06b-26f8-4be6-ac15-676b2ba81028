import React from 'react'
import Card from '../ui/Card'

interface Step {
  number: number
  content: string
}

interface StepsListProps {
  steps: Step[]
  className?: string
}

export default function StepsList({ steps, className = '' }: StepsListProps) {
  return (
    <Card variant="steps" className={`my-3 ${className}`}>
      {steps.map((step, index) => (
        <div key={index} className={`flex items-start ${index < steps.length - 1 ? 'mb-3' : ''}`}>
          <div className="w-6 h-6 rounded-full bg-ios-blue text-white flex items-center justify-center text-xs font-semibold mr-3 flex-shrink-0 mt-0.5">
            {step.number}
          </div>
          <div className="text-[15px] leading-relaxed text-ios-gray-900">
            {step.content}
          </div>
        </div>
      ))}
    </Card>
  )
}
