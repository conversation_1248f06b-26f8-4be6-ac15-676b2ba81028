<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 美好生活</title>
    <link rel="stylesheet" href="../assets/css/mobile-app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .settings-container {
            height: 100%;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 顶部导航栏 */
        .nav-header {
            background: #fff;
            height: 88px;
            padding: 54px 20px 16px;
            border-bottom: 1px solid #e5e5e7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 100;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }

        /* 主内容区域 */
        .settings-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        /* 用户信息卡片 */
        .user-profile {
            background: #fff;
            padding: 24px 20px;
            border-bottom: 1px solid #f2f2f7;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-avatar {
            width: 64px;
            height: 64px;
            border-radius: 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 24px;
        }

        .user-info h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 4px;
        }

        .user-info p {
            font-size: 14px;
            color: #8e8e93;
            margin: 0;
        }

        .edit-profile {
            margin-left: auto;
            color: #007aff;
            font-size: 14px;
            cursor: pointer;
        }

        /* 设置分组 */
        .settings-group {
            background: #fff;
            margin-bottom: 20px;
        }

        .group-title {
            padding: 16px 20px 8px;
            font-size: 14px;
            font-weight: 600;
            color: #8e8e93;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .settings-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .setting-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f2f2f7;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .setting-item:hover {
            background: #f8f9fa;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 16px;
            color: #fff;
        }

        .setting-icon.notification {
            background: #ff3b30;
        }

        .setting-icon.privacy {
            background: #007aff;
        }

        .setting-icon.data {
            background: #34c759;
        }

        .setting-icon.appearance {
            background: #af52de;
        }

        .setting-icon.language {
            background: #ff9500;
        }

        .setting-icon.help {
            background: #8e8e93;
        }

        .setting-icon.about {
            background: #ff2d92;
        }

        .setting-content {
            flex: 1;
        }

        .setting-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 2px;
        }

        .setting-description {
            font-size: 14px;
            color: #8e8e93;
        }

        .setting-action {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #8e8e93;
        }

        .setting-value {
            font-size: 14px;
            color: #8e8e93;
        }

        .setting-arrow {
            font-size: 12px;
        }

        /* 开关按钮 */
        .toggle-switch {
            position: relative;
            width: 48px;
            height: 28px;
            background: #e5e5ea;
            border-radius: 14px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .toggle-switch.active {
            background: #34c759;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 24px;
            height: 24px;
            background: #fff;
            border-radius: 12px;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active::before {
            transform: translateX(20px);
        }

        /* 版本信息 */
        .version-info {
            text-align: center;
            padding: 40px 20px;
            color: #8e8e93;
        }

        .app-logo {
            width: 64px;
            height: 64px;
            border-radius: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: #fff;
            font-size: 24px;
        }

        .app-name {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 4px;
        }

        .app-version {
            font-size: 14px;
            color: #8e8e93;
            margin-bottom: 8px;
        }

        .app-description {
            font-size: 12px;
            color: #8e8e93;
            line-height: 1.4;
        }

        /* 危险操作 */
        .danger-section {
            background: #fff;
            margin-top: 20px;
        }

        .danger-item {
            color: #ff3b30;
        }

        .danger-item .setting-title {
            color: #ff3b30;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-frame">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="time">9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <div class="wifi-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="battery">
                        <div class="battery-level"></div>
                    </div>
                </div>
            </div>

            <!-- App Content -->
            <div class="app-content">
                <div class="settings-container">
                    <!-- 导航栏 -->
                    <div class="nav-header">
                        <div class="nav-left">
                            <button class="back-btn" onclick="history.back()" title="返回">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <div class="nav-title">设置</div>
                        </div>
                    </div>

                    <!-- 主内容 -->
                    <div class="settings-content">
                        <!-- 用户信息 -->
                        <div class="user-profile">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-info">
                                <h3>张小明</h3>
                                <p>25岁 · 程序员 · 北京</p>
                            </div>
                            <div class="edit-profile">
                                编辑 <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>

                        <!-- 通知设置 -->
                        <div class="settings-group">
                            <div class="group-title">通知</div>
                            <ul class="settings-list">
                                <li class="setting-item">
                                    <div class="setting-icon notification">
                                        <i class="fas fa-bell"></i>
                                    </div>
                                    <div class="setting-content">
                                        <div class="setting-title">推送通知</div>
                                        <div class="setting-description">接收重要提醒和更新</div>
                                    </div>
                                    <div class="toggle-switch active"></div>
                                </li>
                                <li class="setting-item">
                                    <div class="setting-icon notification">
                                        <i class="fas fa-calendar-check"></i>
                                    </div>
                                    <div class="setting-content">
                                        <div class="setting-title">计划提醒</div>
                                        <div class="setting-description">愿景实现进度提醒</div>
                                    </div>
                                    <div class="toggle-switch active"></div>
                                </li>
                            </ul>
                        </div>

                        <!-- 隐私与安全 -->
                        <div class="settings-group">
                            <div class="group-title">隐私与安全</div>
                            <ul class="settings-list">
                                <li class="setting-item">
                                    <div class="setting-icon privacy">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="setting-content">
                                        <div class="setting-title">隐私设置</div>
                                        <div class="setting-description">管理数据隐私选项</div>
                                    </div>
                                    <div class="setting-action">
                                        <i class="fas fa-chevron-right setting-arrow"></i>
                                    </div>
                                </li>
                                <li class="setting-item">
                                    <div class="setting-icon data">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="setting-content">
                                        <div class="setting-title">数据管理</div>
                                        <div class="setting-description">导出或删除个人数据</div>
                                    </div>
                                    <div class="setting-action">
                                        <i class="fas fa-chevron-right setting-arrow"></i>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <!-- 个性化 -->
                        <div class="settings-group">
                            <div class="group-title">个性化</div>
                            <ul class="settings-list">
                                <li class="setting-item">
                                    <div class="setting-icon appearance">
                                        <i class="fas fa-palette"></i>
                                    </div>
                                    <div class="setting-content">
                                        <div class="setting-title">外观</div>
                                        <div class="setting-description">主题和显示设置</div>
                                    </div>
                                    <div class="setting-action">
                                        <span class="setting-value">浅色</span>
                                        <i class="fas fa-chevron-right setting-arrow"></i>
                                    </div>
                                </li>
                                <li class="setting-item">
                                    <div class="setting-icon language">
                                        <i class="fas fa-globe"></i>
                                    </div>
                                    <div class="setting-content">
                                        <div class="setting-title">语言</div>
                                        <div class="setting-description">选择应用语言</div>
                                    </div>
                                    <div class="setting-action">
                                        <span class="setting-value">中文</span>
                                        <i class="fas fa-chevron-right setting-arrow"></i>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <!-- 帮助与支持 -->
                        <div class="settings-group">
                            <div class="group-title">帮助与支持</div>
                            <ul class="settings-list">
                                <li class="setting-item">
                                    <div class="setting-icon help">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                    <div class="setting-content">
                                        <div class="setting-title">帮助中心</div>
                                        <div class="setting-description">常见问题和使用指南</div>
                                    </div>
                                    <div class="setting-action">
                                        <i class="fas fa-chevron-right setting-arrow"></i>
                                    </div>
                                </li>
                                <li class="setting-item">
                                    <div class="setting-icon help">
                                        <i class="fas fa-comment"></i>
                                    </div>
                                    <div class="setting-content">
                                        <div class="setting-title">意见反馈</div>
                                        <div class="setting-description">告诉我们您的想法</div>
                                    </div>
                                    <div class="setting-action">
                                        <i class="fas fa-chevron-right setting-arrow"></i>
                                    </div>
                                </li>
                                <li class="setting-item">
                                    <div class="setting-icon about">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="setting-content">
                                        <div class="setting-title">关于我们</div>
                                        <div class="setting-description">应用信息和团队介绍</div>
                                    </div>
                                    <div class="setting-action">
                                        <i class="fas fa-chevron-right setting-arrow"></i>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <!-- 版本信息 -->
                        <div class="version-info">
                            <div class="app-logo">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="app-name">美好生活</div>
                            <div class="app-version">版本 1.0.0</div>
                            <div class="app-description">
                                让AI帮助您实现人生愿景<br>
                                © 2024 美好生活团队
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 开关按钮交互
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        });
    </script>
</body>
</html>
