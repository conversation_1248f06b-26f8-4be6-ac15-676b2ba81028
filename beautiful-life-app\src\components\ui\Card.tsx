import React from 'react'

interface CardProps {
  children: React.ReactNode
  variant?: 'default' | 'gradient' | 'vision' | 'steps' | 'todo'
  className?: string
  onClick?: () => void
}

export default function Card({ 
  children, 
  variant = 'default', 
  className = '',
  onClick 
}: CardProps) {
  const baseClasses = 'rounded-xl p-4 transition-all duration-200'
  
  const variantClasses = {
    default: 'bg-white border border-ios-gray-100 shadow-sm',
    gradient: 'bg-gradient-to-br from-pink-400 to-purple-300 text-white',
    vision: 'bg-gradient-to-br from-pink-400 to-purple-300 text-white',
    steps: 'bg-ios-gray-50 border border-ios-gray-100',
    todo: 'bg-yellow-50 border border-yellow-200'
  }
  
  const classes = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${onClick ? 'cursor-pointer hover:shadow-md hover:-translate-y-0.5' : ''}
    ${className}
  `.trim()
  
  return (
    <div className={classes} onClick={onClick}>
      {children}
    </div>
  )
}
