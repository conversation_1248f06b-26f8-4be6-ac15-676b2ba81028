import React from 'react'

interface AvatarProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'user' | 'ai'
  icon?: string
  className?: string
}

export default function Avatar({ 
  size = 'md', 
  variant = 'user',
  icon,
  className = ''
}: AvatarProps) {
  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-20 h-20 text-2xl'
  }
  
  const variantClasses = {
    user: 'bg-gradient-to-br from-indigo-500 to-purple-600',
    ai: 'bg-gradient-to-br from-indigo-500 to-purple-600'
  }
  
  const defaultIcon = variant === 'ai' ? 'fas fa-robot' : 'fas fa-user'
  
  const classes = `
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    rounded-full flex items-center justify-center text-white font-medium
    ${className}
  `.trim()
  
  return (
    <div className={classes}>
      <i className={icon || defaultIcon}></i>
    </div>
  )
}
