'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import IPhoneContainer from '@/components/layout/IPhoneContainer'
import StatusBar from '@/components/layout/StatusBar'

export default function SplashPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
      // Auto navigate to home after 3 seconds
      setTimeout(() => {
        router.push('/')
      }, 1000)
    }, 2000)

    return () => clearTimeout(timer)
  }, [router])

  return (
    <IPhoneContainer>
      {/* Status Bar */}
      <StatusBar textColor="white" />
      
      {/* Splash Content */}
      <div 
        className="h-full relative overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%), url("https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80")',
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        {/* Floating elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-10 w-16 h-16 bg-white/20 rounded-full animate-bounce" style={{ animationDelay: '0s', animationDuration: '3s' }}></div>
          <div className="absolute top-40 right-8 w-12 h-12 bg-white/15 rounded-full animate-bounce" style={{ animationDelay: '1s', animationDuration: '4s' }}></div>
          <div className="absolute bottom-40 left-6 w-20 h-20 bg-white/10 rounded-full animate-bounce" style={{ animationDelay: '2s', animationDuration: '5s' }}></div>
          <div className="absolute bottom-60 right-12 w-8 h-8 bg-white/25 rounded-full animate-bounce" style={{ animationDelay: '0.5s', animationDuration: '3.5s' }}></div>
        </div>
        
        {/* Main content */}
        <div className="flex flex-col items-center justify-center h-full text-white text-center px-8 relative z-10">
          {/* Logo/Icon */}
          <div className="mb-8 relative">
            <div className="w-24 h-24 bg-white/20 rounded-3xl flex items-center justify-center backdrop-blur-sm border border-white/30 mb-4 animate-pulse">
              <i className="fas fa-sun text-4xl text-white"></i>
            </div>
            <div className="absolute -inset-4 bg-white/10 rounded-full animate-ping"></div>
          </div>
          
          {/* App name */}
          <h1 className="text-4xl font-bold mb-4 tracking-tight">
            美好生活
          </h1>
          
          {/* Tagline */}
          <p className="text-lg opacity-90 mb-8 leading-relaxed">
            让AI助你实现<br />
            每一个生活愿景
          </p>
          
          {/* Loading indicator */}
          {isLoading ? (
            <div className="flex flex-col items-center">
              <div className="w-8 h-8 border-2 border-white/30 border-t-white rounded-full animate-spin mb-4"></div>
              <p className="text-sm opacity-75">正在启动...</p>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <div className="w-8 h-8 flex items-center justify-center mb-4">
                <i className="fas fa-check text-2xl text-green-400"></i>
              </div>
              <p className="text-sm opacity-75">启动完成</p>
            </div>
          )}
        </div>
        
        {/* Bottom gradient overlay */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/30 to-transparent"></div>
      </div>
    </IPhoneContainer>
  )
}
