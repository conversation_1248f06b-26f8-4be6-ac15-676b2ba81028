'use client'

import React, { useState } from 'react'
import IPhoneContainer from '@/components/layout/IPhoneContainer'
import StatusBar from '@/components/layout/StatusBar'
import NavigationHeader from '@/components/layout/NavigationHeader'
import Sidebar from '@/components/layout/Sidebar'
import ChatContainer from '@/components/pages/ChatContainer'

export default function HomePage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const handleMenuClick = () => {
    setSidebarOpen(true)
  }

  const handleSidebarClose = () => {
    setSidebarOpen(false)
  }

  const menuItems = [
    { 
      icon: "fas fa-heart", 
      label: "我的愿景",
      onClick: () => {
        console.log('Navigate to My Visions')
        setSidebarOpen(false)
      }
    },
    { 
      icon: "fas fa-brain", 
      label: "洞察",
      onClick: () => {
        console.log('Navigate to Insights')
        setSidebarOpen(false)
      }
    },
    { 
      icon: "fas fa-cog", 
      label: "设置",
      onClick: () => {
        console.log('Navigate to Settings')
        setSidebarOpen(false)
      }
    }
  ]

  return (
    <IPhoneContainer>
      {/* Status Bar */}
      <StatusBar textColor="black" />
      
      {/* App Content */}
      <div className="h-full bg-gray-50 flex flex-col relative pt-[54px]">
        {/* Sidebar */}
        <Sidebar 
          isOpen={sidebarOpen}
          onClose={handleSidebarClose}
          menuItems={menuItems}
        />
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col relative">
          {/* Navigation Header */}
          <NavigationHeader 
            title="美好生活助手"
            onMenuClick={handleMenuClick}
          />
          
          {/* Chat Container */}
          <ChatContainer className="flex-1" />
        </div>
      </div>
    </IPhoneContainer>
  )
}
