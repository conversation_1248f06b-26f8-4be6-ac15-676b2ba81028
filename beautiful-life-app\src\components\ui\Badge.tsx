import React from 'react'

interface BadgeProps {
  children: React.ReactNode
  variant?: 'success' | 'warning' | 'info' | 'default'
  size?: 'sm' | 'md'
  className?: string
}

export default function Badge({ 
  children, 
  variant = 'default', 
  size = 'sm',
  className = ''
}: BadgeProps) {
  const baseClasses = 'inline-block font-semibold rounded-full'
  
  const sizeClasses = {
    sm: 'px-3 py-1 text-xs',
    md: 'px-4 py-1.5 text-sm'
  }
  
  const variantClasses = {
    default: 'bg-ios-gray-100 text-ios-gray-700',
    success: 'bg-green-500/80 text-white',
    warning: 'bg-yellow-500/80 text-white',
    info: 'bg-ios-blue/80 text-white'
  }
  
  const classes = `
    ${baseClasses}
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `.trim()
  
  return (
    <span className={classes}>
      {children}
    </span>
  )
}
