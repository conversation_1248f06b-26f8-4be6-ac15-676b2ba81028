'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import IPhoneContainer from '@/components/layout/IPhoneContainer'
import StatusBar from '@/components/layout/StatusBar'
import NavigationHeader from '@/components/layout/NavigationHeader'
import Card from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import Button from '@/components/ui/Button'

interface Vision {
  id: string
  title: string
  description: string
  progress: number
  status: 'active' | 'completed' | 'paused'
  category: string
  deadline: string
}

export default function MyVisionsPage() {
  const router = useRouter()
  
  const [visions] = useState<Vision[]>([
    {
      id: '1',
      title: '🏖️ 东南亚海边生活',
      description: '在30岁前在泰国或马来西亚购买海景房，实现远程工作的自由生活',
      progress: 35,
      status: 'active',
      category: '生活方式',
      deadline: '2027年12月'
    },
    {
      id: '2',
      title: '💼 创业梦想',
      description: '建立自己的科技公司，专注于AI和移动应用开发',
      progress: 60,
      status: 'active',
      category: '事业发展',
      deadline: '2025年6月'
    },
    {
      id: '3',
      title: '🎓 技能提升',
      description: '掌握三门编程语言，获得云计算认证',
      progress: 80,
      status: 'active',
      category: '学习成长',
      deadline: '2024年12月'
    },
    {
      id: '4',
      title: '💪 健康生活',
      description: '养成规律运动习惯，保持理想体重',
      progress: 100,
      status: 'completed',
      category: '健康',
      deadline: '已完成'
    }
  ])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="info">进行中</Badge>
      case 'completed':
        return <Badge variant="success">已完成</Badge>
      case 'paused':
        return <Badge variant="warning">暂停</Badge>
      default:
        return <Badge>未知</Badge>
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500'
    if (progress >= 50) return 'bg-blue-500'
    if (progress >= 30) return 'bg-yellow-500'
    return 'bg-gray-400'
  }

  return (
    <IPhoneContainer>
      <StatusBar textColor="black" />
      
      <div className="h-full bg-gray-50 flex flex-col pt-[54px]">
        <NavigationHeader 
          title="我的愿景"
          leftButton={
            <Button variant="icon" size="md" onClick={() => router.back()}>
              <i className="fas fa-arrow-left"></i>
            </Button>
          }
        />
        
        <div className="flex-1 overflow-y-auto px-5 py-4">
          {/* Stats Overview */}
          <div className="grid grid-cols-3 gap-3 mb-6">
            <Card className="text-center">
              <div className="text-2xl font-bold text-ios-blue mb-1">4</div>
              <div className="text-xs text-ios-gray-500">总愿景</div>
            </Card>
            <Card className="text-center">
              <div className="text-2xl font-bold text-green-500 mb-1">1</div>
              <div className="text-xs text-ios-gray-500">已完成</div>
            </Card>
            <Card className="text-center">
              <div className="text-2xl font-bold text-yellow-500 mb-1">3</div>
              <div className="text-xs text-ios-gray-500">进行中</div>
            </Card>
          </div>
          
          {/* Search and Filter */}
          <div className="flex gap-3 mb-4">
            <div className="flex-1 relative">
              <input 
                type="text" 
                placeholder="搜索愿景..."
                className="w-full px-4 py-2 bg-white border border-ios-gray-100 rounded-lg text-sm focus:outline-none focus:border-ios-blue"
              />
              <i className="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-ios-gray-400"></i>
            </div>
            <Button variant="secondary" size="md">
              <i className="fas fa-filter mr-2"></i>
              筛选
            </Button>
          </div>
          
          {/* Visions List */}
          <div className="space-y-4">
            {visions.map((vision) => (
              <Card 
                key={vision.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => router.push(`/vision-detail?id=${vision.id}`)}
              >
                <div className="flex justify-between items-start mb-3">
                  <h3 className="font-semibold text-ios-gray-900 flex-1 mr-2">
                    {vision.title}
                  </h3>
                  {getStatusBadge(vision.status)}
                </div>
                
                <p className="text-sm text-ios-gray-600 mb-3 leading-relaxed">
                  {vision.description}
                </p>
                
                <div className="flex items-center justify-between mb-3">
                  <span className="text-xs text-ios-gray-500">{vision.category}</span>
                  <span className="text-xs text-ios-gray-500">{vision.deadline}</span>
                </div>
                
                {/* Progress Bar */}
                <div className="mb-2">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-ios-gray-500">进度</span>
                    <span className="text-xs font-medium text-ios-gray-700">{vision.progress}%</span>
                  </div>
                  <div className="w-full bg-ios-gray-100 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(vision.progress)}`}
                      style={{ width: `${vision.progress}%` }}
                    ></div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          {/* Add New Vision Button */}
          <div className="mt-6 pb-8">
            <Button 
              variant="primary" 
              size="lg"
              className="w-full"
              onClick={() => console.log('Add new vision')}
            >
              <i className="fas fa-plus mr-2"></i>
              添加新愿景
            </Button>
          </div>
        </div>
      </div>
    </IPhoneContainer>
  )
}
