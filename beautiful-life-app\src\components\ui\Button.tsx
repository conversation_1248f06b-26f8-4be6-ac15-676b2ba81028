import React from 'react'

interface ButtonProps {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'icon'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  onClick?: () => void
  className?: string
  type?: 'button' | 'submit' | 'reset'
}

export default function Button({ 
  children, 
  variant = 'primary', 
  size = 'md',
  disabled = false,
  onClick,
  className = '',
  type = 'button'
}: ButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2'
  
  const variantClasses = {
    primary: 'bg-ios-blue text-white hover:bg-blue-600 focus:ring-ios-blue disabled:bg-ios-gray-300',
    secondary: 'bg-ios-gray-50 text-ios-gray-900 hover:bg-ios-gray-100 focus:ring-ios-gray-300 disabled:bg-ios-gray-100',
    icon: 'bg-ios-gray-50 text-ios-blue hover:bg-ios-gray-100 hover:scale-105 focus:ring-ios-blue disabled:bg-ios-gray-100'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm rounded-lg',
    md: 'px-4 py-2 text-base rounded-xl',
    lg: 'px-6 py-3 text-lg rounded-2xl'
  }
  
  const iconSizeClasses = {
    sm: 'w-6 h-6 rounded-lg text-sm',
    md: 'w-8 h-8 rounded-2xl text-base',
    lg: 'w-10 h-10 rounded-2xl text-lg'
  }
  
  const classes = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${variant === 'icon' ? iconSizeClasses[size] : sizeClasses[size]}
    ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}
    ${className}
  `.trim()
  
  return (
    <button
      type={type}
      className={classes}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  )
}
