{"version": 3, "sources": ["../../../../src/server/web/spec-extension/request.ts"], "names": ["INTERNALS", "NextRequest", "Symbol", "Request", "constructor", "input", "init", "url", "String", "validateURL", "nextUrl", "NextURL", "headers", "toNodeOutgoingHttpHeaders", "nextConfig", "cookies", "RequestCookies", "geo", "ip", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "toString", "for", "bodyUsed", "cache", "credentials", "destination", "Object", "fromEntries", "integrity", "keepalive", "method", "mode", "redirect", "referrer", "referrerPolicy", "signal", "page", "RemovedPageError", "ua", "RemovedUAError"], "mappings": ";;;;;;;;;;;;;;;IAOaA,SAAS;eAATA;;IAEAC,WAAW;eAAXA;;;yBAPW;uBAC+B;uBACN;yBAClB;AAExB,MAAMD,YAAYE,OAAO;AAEzB,MAAMD,oBAAoBE;IAS/BC,YAAYC,KAAwB,EAAEC,OAAoB,CAAC,CAAC,CAAE;QAC5D,MAAMC,MACJ,OAAOF,UAAU,YAAY,SAASA,QAAQA,MAAME,GAAG,GAAGC,OAAOH;QACnEI,IAAAA,kBAAW,EAACF;QACZ,IAAIF,iBAAiBF,SAAS,KAAK,CAACE,OAAOC;aACtC,KAAK,CAACC,KAAKD;QAChB,MAAMI,UAAU,IAAIC,gBAAO,CAACJ,KAAK;YAC/BK,SAASC,IAAAA,gCAAyB,EAAC,IAAI,CAACD,OAAO;YAC/CE,YAAYR,KAAKQ,UAAU;QAC7B;QACA,IAAI,CAACd,UAAU,GAAG;YAChBe,SAAS,IAAIC,uBAAc,CAAC,IAAI,CAACJ,OAAO;YACxCK,KAAKX,KAAKW,GAAG,IAAI,CAAC;YAClBC,IAAIZ,KAAKY,EAAE;YACXR;YACAH,KAAKY,QAAQC,GAAG,CAACC,kCAAkC,GAC/Cd,MACAG,QAAQY,QAAQ;QACtB;IACF;IAEA,CAACpB,OAAOqB,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLR,SAAS,IAAI,CAACA,OAAO;YACrBE,KAAK,IAAI,CAACA,GAAG;YACbC,IAAI,IAAI,CAACA,EAAE;YACXR,SAAS,IAAI,CAACA,OAAO;YACrBH,KAAK,IAAI,CAACA,GAAG;YACb,kCAAkC;YAClCiB,UAAU,IAAI,CAACA,QAAQ;YACvBC,OAAO,IAAI,CAACA,KAAK;YACjBC,aAAa,IAAI,CAACA,WAAW;YAC7BC,aAAa,IAAI,CAACA,WAAW;YAC7Bf,SAASgB,OAAOC,WAAW,CAAC,IAAI,CAACjB,OAAO;YACxCkB,WAAW,IAAI,CAACA,SAAS;YACzBC,WAAW,IAAI,CAACA,SAAS;YACzBC,QAAQ,IAAI,CAACA,MAAM;YACnBC,MAAM,IAAI,CAACA,IAAI;YACfC,UAAU,IAAI,CAACA,QAAQ;YACvBC,UAAU,IAAI,CAACA,QAAQ;YACvBC,gBAAgB,IAAI,CAACA,cAAc;YACnCC,QAAQ,IAAI,CAACA,MAAM;QACrB;IACF;IAEA,IAAWtB,UAAU;QACnB,OAAO,IAAI,CAACf,UAAU,CAACe,OAAO;IAChC;IAEA,IAAWE,MAAM;QACf,OAAO,IAAI,CAACjB,UAAU,CAACiB,GAAG;IAC5B;IAEA,IAAWC,KAAK;QACd,OAAO,IAAI,CAAClB,UAAU,CAACkB,EAAE;IAC3B;IAEA,IAAWR,UAAU;QACnB,OAAO,IAAI,CAACV,UAAU,CAACU,OAAO;IAChC;IAEA;;;;GAIC,GACD,IAAW4B,OAAO;QAChB,MAAM,IAAIC,uBAAgB;IAC5B;IAEA;;;;GAIC,GACD,IAAWC,KAAK;QACd,MAAM,IAAIC,qBAAc;IAC1B;IAEA,IAAWlC,MAAM;QACf,OAAO,IAAI,CAACP,UAAU,CAACO,GAAG;IAC5B;AACF"}